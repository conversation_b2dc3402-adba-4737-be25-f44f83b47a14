/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'sr-latn', {
	find: 'Pretraga',
	findOptions: 'Find Options',
	findWhat: 'Pronadi:',
	matchCase: '<PERSON>zlik<PERSON>j mala i velika slova',
	matchCyclic: 'Match cyclic',
	matchWord: 'Uporedi cele reci',
	notFoundMsg: 'Traženi tekst nije pronađen.',
	replace: '<PERSON><PERSON><PERSON>',
	replaceAll: 'Zameni sve',
	replaceSuccessMsg: '%1 occurrence(s) replaced.',
	replaceWith: '<PERSON>ameni sa:',
	title: 'Find and Replace'
} );
