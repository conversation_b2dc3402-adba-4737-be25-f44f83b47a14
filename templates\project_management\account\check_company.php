<?php
/**
 * AJAX endpoint để kiểm tra tên công ty có tồn tại trong hệ thống không
 */

// Bắt đầu session
session_start();

// Include database configuration
require_once '../config/database.php';

// Set content type
header('Content-Type: application/json');

// Chỉ cho phép POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Lấy dữ liệu từ POST
$input = json_decode(file_get_contents('php://input'), true);
$company_name = isset($input['company_name']) ? trim($input['company_name']) : '';

// Validate input
if (empty($company_name)) {
    echo json_encode([
        'exists' => false,
        'message' => '<PERSON><PERSON> lòng nhập tên công ty'
    ]);
    exit;
}

try {
    // Kiểm tra xem công ty có tồn tại không
    $stmt = $pdo->prepare("
        SELECT u.id, u.username, u.user_type, u.password, c.id as company_id, c.name as company_name
        FROM project_users u 
        LEFT JOIN project_companies c ON u.company_id = c.id 
        WHERE u.username = ? AND u.user_type IN ('admin', 'customer')
    ");
    $stmt->execute([$company_name]);
    $user = $stmt->fetch();

    if ($user) {
        // Công ty tồn tại
        $response = [
            'exists' => true,
            'user_type' => $user['user_type'],
            'has_password' => !empty($user['password']),
            'message' => 'Công ty tồn tại trong hệ thống'
        ];

        // Nếu là admin hoặc customer đã có password
        if ($user['user_type'] === 'admin' || !empty($user['password'])) {
            $response['need_password'] = true;
            $response['message'] = 'Vui lòng nhập mật khẩu để đăng nhập';
        } else {
            // Customer chưa có password
            $response['need_password'] = false;
            $response['message'] = 'Tài khoản chưa được kích hoạt. Vui lòng liên hệ admin để đặt mật khẩu.';
        }
    } else {
        // Công ty không tồn tại
        $response = [
            'exists' => false,
            'message' => 'Tên công ty không tồn tại trong hệ thống'
        ];
    }

    echo json_encode($response);

} catch (PDOException $e) {
    error_log("Check company error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'error' => 'Có lỗi xảy ra khi kiểm tra thông tin công ty'
    ]);
}
?>
