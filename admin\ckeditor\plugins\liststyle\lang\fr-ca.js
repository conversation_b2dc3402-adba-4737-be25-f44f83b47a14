/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'fr-ca', {
	armenian: 'Numération arménienne',
	bulletedTitle: 'Propriété de liste à puce',
	circle: 'Cercle',
	decimal: 'Décimal (1, 2, 3, etc.)',
	decimalLeadingZero: 'Décimal avec zéro (01, 02, 03, etc.)',
	disc: 'Disque',
	georgian: 'Numération géorgienne (an, ban, gan, etc.)',
	lowerAlpha: 'Alphabétique minuscule (a, b, c, d, e, etc.)',
	lowerGreek: 'Grecque minuscule (alpha, beta, gamma, etc.)',
	lowerRoman: 'Romain minuscule (i, ii, iii, iv, v, etc.)',
	none: 'Aucun',
	notset: '<non défini>',
	numberedTitle: 'Propriété de la liste numérotée',
	square: 'Carré',
	start: 'Début',
	type: 'Type',
	upperAlpha: 'Alphabétique majuscule (A, B, C, D, E, etc.)',
	upperRoman: 'Romain Majuscule (I, II, III, IV, V, etc.)',
	validateStartNumber: 'Le numéro de début de liste doit être un entier.'
} );
