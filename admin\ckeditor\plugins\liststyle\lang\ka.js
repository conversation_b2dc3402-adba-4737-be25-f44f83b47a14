/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'ka', {
	armenian: 'სომხური გადანომრვა',
	bulletedTitle: 'ღილებიანი სიის პარამეტრები',
	circle: 'წრეწირი',
	decimal: 'რიცხვებით (1, 2, 3, ..)',
	decimalLeadingZero: 'ნულით დაწყებული რიცხვებით (01, 02, 03, ..)',
	disc: 'წრე',
	georgian: 'ქართული გადანომრვა (ან, ბან, გან, ..)',
	lowerAlpha: 'პატარა ლათინური ასოებით (a, b, c, d, e, ..)',
	lowerGreek: 'პატარა ბერძნული ასოებით (ალფა, ბეტა, გამა, ..)',
	lowerRoman: 'რომაული გადანომრვცა პატარა ციფრებით (i, ii, iii, iv, v, ..)',
	none: 'არაფერი',
	notset: '<არაფერი>',
	numberedTitle: 'გადანომრილი სიის პარამეტრები',
	square: 'კვადრატი',
	start: 'საწყისი',
	type: 'ტიპი',
	upperAlpha: 'დიდი ლათინური ასოებით (A, B, C, D, E, ..)',
	upperRoman: 'რომაული გადანომრვა დიდი ციფრებით (I, II, III, IV, V, etc.)',
	validateStartNumber: 'სიის საწყისი მთელი რიცხვი უნდა იყოს.'
} );
