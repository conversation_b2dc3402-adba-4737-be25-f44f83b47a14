/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'af', {
	armenian: 'Armeense nommering',
	bulletedTitle: '<PERSON><PERSON><PERSON><PERSON> van ongenommerde lys',
	circle: 'Sir<PERSON>',
	decimal: '<PERSON><PERSON>le syfers (1, 2, 3, ens.)',
	decimalLeadingZero: '<PERSON><PERSON><PERSON> syfers met voorloopnul (01, 02, 03, ens.)',
	disc: 'Skyf',
	georgian: '<PERSON><PERSON> nommering (an, ban, gan, ens.)',
	lowerAlpha: 'Kleinletters (a, b, c, d, e, ens.)',
	lowerGreek: 'Griekse kleinletters (alpha, beta, gamma, ens.)',
	lowerRoman: 'Romeinse kleinletters (i, ii, iii, iv, v, ens.)',
	none: 'Geen',
	notset: '<nie ingestel nie>',
	numberedTitle: '<PERSON><PERSON><PERSON><PERSON> van genommerde lys',
	square: '<PERSON><PERSON><PERSON><PERSON>',
	start: 'Begin',
	type: 'Tipe',
	upperAlpha: 'Hoofletters (A, B, C, D, E, ens.)',
	upperRoman: 'Romeinse hoofletters (I, II, III, IV, V, ens.)',
	validateStartNumber: 'Beginnommer van lys moet \'n heelgetal wees.'
} );
