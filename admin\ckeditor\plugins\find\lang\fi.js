/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'fi', {
	find: 'Etsi',
	findOptions: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
	findWhat: 'Etsi mitä:',
	matchCase: '<PERSON><PERSON> kirjainkoko',
	matchCyclic: 'Kierrä ympäri',
	matchWord: 'Koko sana',
	notFoundMsg: 'Etsittyä tekstiä ei löytynyt.',
	replace: '<PERSON><PERSON><PERSON>',
	replaceAll: 'Korvaa kaikki',
	replaceSuccessMsg: '%1 esiintymä(ä) korvattu.',
	replaceWith: '<PERSON><PERSON><PERSON> tällä:',
	title: '<PERSON>tsi ja korvaa'
} );
