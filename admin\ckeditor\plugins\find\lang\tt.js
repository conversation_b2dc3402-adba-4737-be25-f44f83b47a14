/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'tt', {
	find: 'Эзләү',
	findOptions: 'Эзләү көйләүләре',
	findWhat: 'Нәрсә эзләргә:',
	matchCase: 'Баш һәм юл хәрефләрен исәпкә алу',
	matchCyclic: 'Кабатлап эзләргә',
	matchWord: 'Сүзләрне тулысынча гына эзләү',
	notFoundMsg: 'Эзләнгән текст табылмады.',
	replace: 'Алмаштыру',
	replaceAll: 'Барысын да алмаштыру',
	replaceSuccessMsg: '%1 урында(ларда) алмаштырылган.',
	replaceWith: 'Нәрсәгә алмаштыру:',
	title: 'Эзләп табу һәм алмаштыру'
} );
