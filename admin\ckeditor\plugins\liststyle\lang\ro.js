/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'ro', {
	armenian: 'Numerotare armeniană',
	bulletedTitle: 'Proprietățile listei cu simboluri',
	circle: 'Cerc',
	decimal: 'Decimale (1, 2, 3, etc.)',
	decimalLeadingZero: 'Decimale cu zero în față (01, 02, 03, etc.)',
	disc: 'Disc',
	georgian: 'Numerotare georgiană (an, ban, gan, etc.)',
	lowerAlpha: 'Litere mici (a, b, c, d, e, etc.)',
	lowerGreek: 'Litere grecești mici (alpha, beta, gamma, etc.)',
	lowerRoman: 'Cifre romane mici (i, ii, iii, iv, v, etc.)',
	none: 'Nimic',
	notset: '<nesetat>',
	numberedTitle: 'Proprietățile listei numerotate',
	square: 'Pătrat',
	start: 'Start',
	type: 'Tip',
	upperAlpha: 'Litere mari (A, B, C, D, E, etc.)',
	upperRoman: 'Cifre romane mari (I, II, III, IV, V, etc.)',
	validateStartNumber: 'Începutul listei trebuie să fie un număr întreg.'
} );
