/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'az', {
	armenian: '<PERSON><PERSON><PERSON><PERSON> nömrələmə',
	bulletedTitle: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> siyahının xüsusiyyətləri',
	circle: '<PERSON><PERSON><PERSON><PERSON>ci<PERSON>',
	decimal: 'Rəqəm (1, 2, 3 və s.)',
	decimalLeadingZero: 'Aparıcı sıfır olan rəqəm (01, 02, 03 və s.)',
	disc: 'Disk',
	georgian: '<PERSON><PERSON><PERSON><PERSON> nömrələmə (an, ban, gan, və s.)',
	lowerAlpha: 'Kiçik hərflər (a, b, c, d, e və s.)',
	lowerGreek: '<PERSON><PERSON>ik <PERSON> hərfləri (alfa, beta, qamma və s.)',
	lowerRoman: 'Rum rəqəmləri (i, ii, iii, iv, v və s.)',
	none: 'Yoxdur',
	notset: '<seçilməmiş>',
	numberedTitle: '<PERSON><PERSON><PERSON>r<PERSON>li siyahının xüsusiyyətləri',
	square: 'Dördbucaq',
	start: 'Başlanğıc',
	type: 'Növ',
	upperAlpha: 'Böyük hərflər (a, b, c, d, e və s.)',
	upperRoman: 'Böyük Rum rəqəmləri (I, II, III, IV, V və s.)',
	validateStartNumber: 'Siyahının başlanğıc nömrəsi tam və müsbət rəqəm olmalıdır.'
} );
